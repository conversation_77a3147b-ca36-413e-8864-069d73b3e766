# ATMA Auth System

Sistem autentikasi modern menggunakan Vanilla JavaScript, Vite, dan <PERSON> CSS dengan desain 2-sisi yang elegan.

## 🚀 Fitur

- **Desain Modern**: Layout 2-sisi dengan branding di kiri dan form di kanan
- **Responsive**: Tampilan yang optimal di berbagai ukuran layar
- **Validasi Real-time**: Validasi form dengan feedback langsung
- **Separation of Concerns**: <PERSON><PERSON><PERSON><PERSON> yang jelas antara logic dan styling
- **API Integration**: Terintegrasi dengan API backend untuk autentikasi

## 🛠️ Teknologi

- **Vanilla JavaScript**: Tanpa framework, pure JavaScript
- **Vite**: Build tool modern untuk development yang cepat
- **Tailwind CSS**: Utility-first CSS framework
- **Modular Architecture**: Struktur kode yang terorganisir

## 📁 Struktur Proyek

```
src/
├── js/
│   ├── components/
│   │   └── AuthForm.js          # Komponen utama auth
│   ├── services/
│   │   └── authService.js       # Service untuk API calls
│   ├── utils/
│   │   ├── dom.js              # Utility untuk DOM manipulation
│   │   └── validation.js       # Utility untuk validasi
│   └── config/
│       └── api.js              # Konfigurasi API
├── style.css                   # Styling dengan Tailwind
└── main.js                     # Entry point aplikasi
```

## 🎨 Fitur Auth

### Login
- Email dan password validation
- Error handling dengan pesan yang jelas
- Loading state saat proses login

### Register
- Validasi email format
- Password strength validation (min 8 karakter, huruf + angka)
- Confirm password matching
- Terms and conditions agreement
- Real-time validation feedback

### Profile Management
- View user information
- Update profile (username, full name, date of birth, gender, school ID)
- Change password
- Token balance display
- Account deletion

## 🔧 Setup dan Instalasi

1. **Clone atau setup proyek**
   ```bash
   npm create vite@latest . -- --template vanilla
   ```

2. **Install dependencies**
   ```bash
   npm install
   npm install -D tailwindcss postcss autoprefixer
   ```

3. **Setup Tailwind CSS**
   ```bash
   npx tailwindcss init -p
   ```

4. **Jalankan development server**
   ```bash
   npm run dev
   ```

5. **Buka browser**
   ```
   http://localhost:5173
   ```

## 🌐 API Integration

Aplikasi ini terintegrasi dengan API backend di `https://api.chhrone.web.id` dengan endpoint:

- `POST /api/auth/register` - Registrasi user baru
- `POST /api/auth/login` - Login user
- `GET /api/auth/profile` - Ambil data profile
- `PUT /api/auth/profile` - Update profile
- `PUT /api/auth/change-password` - Ganti password
- `GET /api/auth/token-balance` - Cek saldo token
- `DELETE /api/auth/account` - Hapus akun
- `POST /api/auth/logout` - Logout user

## 🎯 Validasi

### Email
- Format email yang valid
- Maksimal 255 karakter

### Password
- Minimal 8 karakter
- Harus mengandung minimal 1 huruf dan 1 angka

### Username
- Hanya alphanumeric
- 3-100 karakter

### Profile Fields
- Full name: maksimal 100 karakter
- Date of birth: format ISO, tidak boleh tanggal masa depan
- Gender: 'male' atau 'female'
- School ID: integer positif

## 🎨 Styling

Menggunakan Tailwind CSS dengan custom components:

- `.btn-primary` - Tombol utama
- `.btn-secondary` - Tombol sekunder
- `.input-field` - Input field styling
- `.form-group` - Group form styling
- `.error-message` - Pesan error
- `.success-message` - Pesan sukses

## 🔒 Security

- Token disimpan di localStorage
- Automatic token attachment untuk authenticated requests
- Logout membersihkan semua data lokal
- Validasi client-side dan server-side

## 📱 Responsive Design

- Desktop: Layout 2-sisi penuh
- Tablet: Layout adaptif
- Mobile: Stack layout untuk kemudahan penggunaan

## 🚀 Build untuk Production

```bash
npm run build
```

File hasil build akan ada di folder `dist/`.

## 📝 Catatan

- Aplikasi ini menggunakan localStorage untuk menyimpan token
- Semua validasi dilakukan di client-side dan server-side
- Error handling yang komprehensif untuk user experience yang baik
- Modular architecture memudahkan maintenance dan pengembangan

## 🤝 Kontribusi

Silakan buat pull request atau issue untuk perbaikan dan penambahan fitur.
